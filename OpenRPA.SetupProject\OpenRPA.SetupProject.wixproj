<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" InitialTargets="EnsureWixToolsetInstalled" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>3.10</ProductVersion>
    <ProjectGuid>70e2e351-ac0d-486d-b357-b890ef20d1c8</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>SXRPA</OutputName>
    <OutputType>Package</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <DefineConstants>Debug</DefineConstants>
    <DefineConstants Condition="'$(PythonVersion)' != ''">$(DefineConstants);PythonVersion=$(PythonVersion)</DefineConstants>
    <DefineConstants Condition="'$(PythonVersion)' == ''">$(DefineConstants);PythonVersion=3.10</DefineConstants>
    <WixVariables>PythonSource=..\python_embedded</WixVariables>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <DefineConstants Condition="'$(PythonVersion)' != ''">$(DefineConstants);PythonVersion=$(PythonVersion)</DefineConstants>
    <DefineConstants Condition="'$(PythonVersion)' == ''">$(DefineConstants);PythonVersion=3.10</DefineConstants>
    <WixVariables>PythonSource=..\python_embedded</WixVariables>
    <SuppressPdbOutput>True</SuppressPdbOutput>
    <VerboseOutput>False</VerboseOutput>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="compOffice.wxs" />
    <Compile Include="compMain.wxs" />
    <Compile Include="compForms.wxs" />
    <Compile Include="compIE.wxs" />
    <Compile Include="compNM.wxs" />
    <Compile Include="compImage.wxs" />
    <Compile Include="compScript.wxs" />
    <Compile Include="compAviRecorder.wxs" />
    <Compile Include="compOpenFlowDB.wxs" />
    <Compile Include="compFileWatcher.wxs" />
    <Compile Include="compJava.wxs" />

    <Compile Include="compPDPlugin.wxs" />
    <Compile Include="compRDServicePlugin.wxs" />
    <Compile Include="compUtilities.wxs" />
    <Compile Include="compUpdater.wxs.old" />
    <Compile Include="compDatabase.wxs" />

    <Compile Include="compPS.wxs" />
    <Compile Include="compStorageLiteDb.wxs" />
    <Compile Include="compStorageFS.wxs" />
    <Compile Include="compPython.wxs" />
    <Compile Include="FeatureTreeDlg.wxs" />
    <Compile Include="mainfiles.wxs" />
    <Compile Include="Product.wxs" />
    <Compile Include="UI.wxs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Product_en-us.wxl" />
  </ItemGroup>
  <ItemGroup>
    <WixExtension Include="WixUtilExtension">
      <HintPath>$(WixExtDir)\WixUtilExtension.dll</HintPath>
      <Name>WixUtilExtension</Name>
    </WixExtension>
    <WixExtension Include="WixUIExtension">
      <HintPath>$(WixExtDir)\WixUIExtension.dll</HintPath>
      <Name>WixUIExtension</Name>
    </WixExtension>
  </ItemGroup>
  <ItemGroup>
    <Content Include="debug.txt" />
    <Content Include="HeatTransform2.xslt" />
    <Content Include="custom.xsl" />
    <Content Include="harvestMain.bat" />
    <Content Include="HeatTransform.xslt" />
    <Content Include="lanEN.wxi" />
    <Content Include="open_rpa.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.SetupActions\OpenRPA.SetupActions.csproj">
      <Name>OpenRPA.SetupActions</Name>
      <Project>{52af10f7-dcf9-4e1d-b4dd-331456671118}</Project>
      <Private>True</Private>
      <DoNotHarvest>True</DoNotHarvest>
      <RefProjectOutputGroups>Binaries;Content;Satellites</RefProjectOutputGroups>
      <RefTargetDir>INSTALLFOLDER</RefTargetDir>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(WixTargetsPath)" Condition=" '$(WixTargetsPath)' != '' " />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets" Condition=" '$(WixTargetsPath)' == '' AND Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets') " />
  <Target Name="EnsureWixToolsetInstalled" Condition=" '$(WixTargetsImported)' != 'true' ">
    <Error Text="The WiX Toolset v3.11 (or newer) build tools must be installed to build this project. To download the WiX Toolset, see http://wixtoolset.org/releases/" />
  </Target>
  <!--
	To modify your build process, add your task inside one of the targets below and uncomment it.
	Other similar extension points exist, see Wix.targets.
	<Target Name="BeforeBuild">
	</Target>
	<Target Name="AfterBuild">
	</Target>
	-->
</Project>
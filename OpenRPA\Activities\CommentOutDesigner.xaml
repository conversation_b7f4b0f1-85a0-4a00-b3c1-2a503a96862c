﻿<sap:ActivityDesigner x:Class="OpenRPA.Activities.CommentOutDesigner"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sap="clr-namespace:System.Activities.Presentation;assembly=System.Activities.Presentation"
    xmlns:sad="clr-namespace:System.Activities.Presentation;assembly=System.Activities.Presentation"
    xmlns:sa="clr-namespace:System.Activities;assembly=System.Activities"
    xmlns:sapv="clr-namespace:System.Activities.Presentation.View;assembly=System.Activities.Presentation" 
    xmlns:or="clr-namespace:OpenRPA.Resources"
    >

    <sap:ActivityDesigner.Icon>
        <DrawingBrush>
            <DrawingBrush.Drawing>
                <ImageDrawing>
                    <ImageDrawing.Rect>
                        <Rect Location="0,0" Size="16,16" ></Rect>
                    </ImageDrawing.Rect>
                    <ImageDrawing.ImageSource>
                        <BitmapImage UriSource="/OpenRPA;component/Resources/designer/commentout.png" ></BitmapImage>
                    </ImageDrawing.ImageSource>
                </ImageDrawing>
            </DrawingBrush.Drawing>
        </DrawingBrush>
    </sap:ActivityDesigner.Icon>




    <sap:ActivityDesigner.Resources>
        <DataTemplate x:Key="Collapsed">
            <Label Padding="0,0,0,0" FontStyle="Italic" Foreground="{x:Static SystemColors.GrayTextBrush}" HorizontalAlignment="Center"
                       VerticalAlignment="Center" Content="{x:Static or:strings.activity_click_to_view}"></Label>
        </DataTemplate>
        <DataTemplate x:Key="Expanded">
            <StackPanel>
                <sad:WorkflowItemPresenter AutomationProperties.AutomationId="Body" 
                                           Item="{Binding Path=ModelItem.Body, Mode=TwoWay}"
                                           AllowedItemType="{x:Type sa:Activity}"
                                           HintText="{x:Static or:strings.activity_drop_hint}"
                         />
            </StackPanel>
        </DataTemplate>
        <Style x:Key="ExpandOrCollapsedStyle" TargetType="{x:Type ContentPresenter}">
            <Setter Property="ContentTemplate" Value="{DynamicResource Expanded}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Path=ShowExpanded}" Value="false">
                    <Setter Property="ContentTemplate" Value="{DynamicResource Collapsed}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </sap:ActivityDesigner.Resources>
    <Grid>
        <ContentPresenter Style="{DynamicResource ExpandOrCollapsedStyle}" Content="{Binding}" />
    </Grid>
    
    

    
</sap:ActivityDesigner>

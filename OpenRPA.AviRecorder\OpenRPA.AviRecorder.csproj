﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <EnableDefaultPageItems>false</EnableDefaultPageItems>
    <Authors><PERSON></Authors>
    <Product>OpenRPA</Product>
    <Description>A few activites to handle recording the screen while a workflow is running, created for OpenRPA robot</Description>
    <PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <Version>1.0.18</Version>
    <PackageReleaseNotes>1.0.18 Change target framework to 4.7.2
1.0.17 Add langauges and bug fixes
1.0.16 update interfaces reference
1.0.11 Make runner respect enabled setting in config
1.0.9 Add runner plugin for auto recording workflows
1.0.8 update interfaces reference</PackageReleaseNotes>
    <PackageIcon>avirecorder.png</PackageIcon>
    <Configurations>Debug;Release;ReleaseNuget;PrepInstaller</Configurations>
    <PackageTags>OpenRPA</PackageTags>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <Platforms>AnyCPU</Platforms>
    <Company>OpenIAP</Company>
    <!-- 限制资源文件只生成中英文 -->
    <SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>..\debug</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <None Include="Resources\avirecorder.png" Pack="true" PackagePath="\" />
    <None Remove="Activities\Playback.xaml" />
    <None Remove="Activities\PlayRecordingDesigner.xaml" />
    <None Remove="Activities\StartRecordingDesigner.xaml" />
    <None Remove="Activities\StopRecordingDesigner.xaml" />
    <None Remove="Resources\avirecorder.png" />
    <None Remove="Resources\designer\playrecording.png" />
    <None Remove="Resources\designer\startrecording.png" />
    <None Remove="Resources\designer\stoprecording.png" />
    <None Remove="Resources\toolbox\playrecording.png" />
    <None Remove="Resources\toolbox\startrecording.png" />
    <None Remove="Resources\toolbox\stoprecording.png" />
    <None Remove="Views\RunPluginView.xaml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\toolbox\playrecording.png" />
    <EmbeddedResource Include="Resources\toolbox\startrecording.png" />
    <EmbeddedResource Include="Resources\toolbox\stoprecording.png" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NAudio" Version="1.9.0" />
    <PackageReference Include="SharpAvi" Version="2.2.1" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Activities\Playback.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\StartRecordingDesigner.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\StopRecordingDesigner.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\RunPluginView.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Activities" />
    <Reference Include="System.Activities.Presentation" />
    <Reference Include="System.Xaml" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Activities\PlayRecordingDesigner.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.Interfaces\OpenRPA.Interfaces.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\designer\playrecording.png" />
    <Resource Include="Resources\designer\startrecording.png" />
    <Resource Include="Resources\designer\stoprecording.png" />
    <Resource Include="Resources\avirecorder.png" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Resources\strings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>strings.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Resources\strings.ro.resx">
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\strings.ru.resx">
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\strings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <ProjectExtensions><VisualStudio><UserProperties BuildVersion_StartDate="2000/1/1" /></VisualStudio></ProjectExtensions>
</Project>

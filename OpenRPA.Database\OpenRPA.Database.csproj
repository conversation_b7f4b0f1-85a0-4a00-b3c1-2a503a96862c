﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <EnableDefaultPageItems>false</EnableDefaultPageItems>
    <Configurations>Debug;Release;PrepInstaller;ReleaseNuget</Configurations>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Authors><PERSON></Authors>
    <Product>OpenRPA</Product>
    <Description>A collection of activities to work with a database using OleDB / ADO, specifically tested and created for OpenRPA robot</Description>
    <PackageReleaseNotes>1.0.2 Set default DataProvider
1.0.1 Update for generic database providers
1.0.0 Initial push</PackageReleaseNotes>
    <PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <PackageIcon>database.png</PackageIcon>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <Platforms>AnyCPU</Platforms>
    <Company>OpenIAP</Company>
    <Version>1.0.2</Version>
    <!-- 限制资源文件只生成简体中文和英文 -->
    <SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>..\debug</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Activities\DatabaseScopeDesigner.xaml" />
    <None Remove="Activities\ExecuteNonQueryDesigner.xaml" />
    <None Remove="Activities\ExecuteQueryQueryDesigner.xaml" />
    <None Remove="Activities\ExecuteScalarDesigner.xaml" />
    <None Remove="Activities\UpdateFromDataTableDesigner.xaml" />
    <None Remove="Resources\designer\databasescope.png" />
    <None Remove="Resources\designer\executenonquery.png" />
    <None Remove="Resources\designer\executequery.png" />
    <None Remove="Resources\designer\executescalar.png" />
    <None Remove="Resources\designer\updatefromdatatable.png" />
    <None Remove="Resources\toolbox\databasescope.png" />
    <None Remove="Resources\toolbox\executenonquery.png" />
    <None Remove="Resources\toolbox\executequery.png" />
    <None Remove="Resources\toolbox\executescalar.png" />
    <None Remove="Resources\toolbox\updatefromdatatable.png" />
  </ItemGroup>
  <!-- COM Reference removed for .NET compatibility -->
  <!-- Use alternative data connection methods for .NET Core/5+ -->
  <ItemGroup>
    <EmbeddedResource Include="Resources\toolbox\databasescope.png" />
    <EmbeddedResource Include="Resources\toolbox\executenonquery.png" />
    <EmbeddedResource Include="Resources\toolbox\executequery.png" />
    <EmbeddedResource Include="Resources\toolbox\executescalar.png" />
    <EmbeddedResource Include="Resources\toolbox\updatefromdatatable.png" />
  </ItemGroup>
  <ItemGroup>
	  <None Include="Resources\database.png" Pack="true" PackagePath="\" />
    <PackageReference Include="DataConnectionDialog" Version="1.2.0" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Activities\DatabaseScopeDesigner.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\ExecuteScalarDesigner.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\UpdateFromDataTableDesigner.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\ExecuteQueryQueryDesigner.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\ExecuteNonQueryDesigner.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.Interfaces\OpenRPA.Interfaces.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="ADODB">
      <HintPath>..\..\..\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\PublicAssemblies\adodb.dll</HintPath>
    </Reference>
    <Reference Include="System.Activities" />
    <Reference Include="System.Activities.Presentation" />
    <Reference Include="System.Xaml" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\designer\databasescope.png" />
    <Resource Include="Resources\designer\executenonquery.png" />
    <Resource Include="Resources\designer\executequery.png" />
    <Resource Include="Resources\designer\executescalar.png" />
    <Resource Include="Resources\designer\updatefromdatatable.png" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Resources\strings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>strings.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Resources\strings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Update="adodb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
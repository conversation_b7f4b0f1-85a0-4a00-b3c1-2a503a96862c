{"dotnet.defaultSolution": "OpenRPA.sln", "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/debug": false, "**/debug86": false, "**/dist": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/debug": true, "**/debug86": true, "**/dist": true, "**/node_modules": true}, "files.associations": {"*.xaml": "xml", "*.config": "xml", "*.resx": "xml", "*.wxs": "xml", "*.wxi": "xml"}, "xml.fileAssociations": [{"pattern": "**/*.xaml", "systemId": "http://schemas.microsoft.com/winfx/2006/xaml/presentation"}], "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.inlayHints.enableInlayHintsForParameters": true, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true, "dotnet.inlayHints.enableInlayHintsForOtherParameters": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true, "workbench.colorCustomizations": {"activityBar.activeBackground": "#4e8b97", "activityBar.background": "#4e8b97", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#dec0d9", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#4e8b97", "statusBar.background": "#3d6c75", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#4e8b97", "statusBarItem.remoteBackground": "#3d6c75", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#3d6c75", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#3d6c7599", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#3d6c75", "dotrush.roslyn.projectOrSolutionFiles": ["d:\\Projects\\net\\openrpa\\OpenRPA.sln"]}
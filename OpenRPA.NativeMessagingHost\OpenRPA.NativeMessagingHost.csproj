<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Authors><PERSON></Authors>
    <Product>OpenRPA</Product>
    <PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <Version>1.0.32</Version>
    <ApplicationIcon />
    <OutputType>Exe</OutputType>
    <StartupObject />
    <PackageReleaseNotes>1.0.32 Fix major issue with special charecters in nm
1.0.31 Improve text updates, change frame messsages to json, to avoid conflict with certain webpages
1.0.30 Add support to update div value
1.0.29 Fix utf encoding with setvalue
1.0.28 Add support for checkboxes
1.0.27 Fix addon after firefox update breaking things
1.0.26 Filter out google sheet in chrome extension
1.0.25 Allow custom unique ids for xpath generation in nm
1.0.24 Add cords for GetElement
1.0.23 Fix issue with multiple frames and selector
1.0.22 improve iframe support / update browser extenstion
1.0.21 add ng-model as a prefered id element
1.0.20 Improve input updates
1.0.19 Remove irrelevant error logging, improve iframe injection
1.0.18 temporary, workaround for iframes late loading
1.0.16 Fix terminal server name pipe issue
1.0.15 Change target framework to 4.7.2
1.0.14 Add IsVisible, Display, isVisibleOnScreen and Disabled to GetDOM
1.0.11 update interfaces reference
1.0.8 update interfaces reference</PackageReleaseNotes>
    <Description>The NativeMessagingHost serves as a proxy between chrome/ff and openrpa</Description>
    <PackageIcon>open_rpa128.png</PackageIcon>
    <Configurations>Debug;Release;ReleaseNuget;PrepInstaller</Configurations>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <PackageTags />
    <Platforms>AnyCPU</Platforms>
    <Company>OpenIAP</Company>
    <!-- 限制资源文件只生成简体中文和英文 -->
    <SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>..\debug</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <None Include="Resources\open_rpa128.png" Pack="true" PackagePath="\" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="background - Copy.js" />
    <None Remove="background.js" />
    <None Remove="chromemanifest.json" />
    <None Remove="ffmanifest.json" />
    <None Remove="jquery.js" />
    <None Remove="libs.js" />
    <None Remove="openrpautil.js" />
    <None Remove="Resources\OpenRPA.ico" />
    <None Remove="Resources\OpenRPA.png" />
    <None Remove="Resources\OpenRPA2.ico" />
    <None Remove="Resources\OpenRPA2.png" />
    <None Remove="zeniverse.js" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="chromemanifest.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ffmanifest.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="background.js" />
    <EmbeddedResource Include="libs.js" />
    <EmbeddedResource Include="jquery.js" />
    <EmbeddedResource Include="openrpautil.js" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.Interfaces\OpenRPA.Interfaces.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\OpenRPA.ico" />
    <Resource Include="Resources\OpenRPA.png" />
    <Resource Include="Resources\OpenRPA2.ico" />
    <Resource Include="Resources\OpenRPA2.png" />
  </ItemGroup>
  <Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <ProjectExtensions><VisualStudio><UserProperties BuildVersion_StartDate="2000/1/1" /></VisualStudio></ProjectExtensions>
</Project>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <ApplicationIcon />
    <OutputType>Exe</OutputType>
    <StartupObject />
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Description>Windows Service to monitor Remote Desktop and keep sessions alive so OpenRPA can work unattended</Description>
    <PackageLicenseExpression>AGPL-3.0-only</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <PackageIcon>terminalemulator.png</PackageIcon>
    <Configurations>Debug;Release;ReleaseNuget;PrepInstaller</Configurations>
    <Version>1.0.18</Version>
    <PackageReleaseNotes>1.0.17 Improve rdservice, add support for running without freerdp as a service
1.0.16 Fix reconnecting bug
1.0.15 Change target framework to 4.7.2
1.0.14 cleanup
1.0.13 Improve startup time of rdservice
1.0.12 Improve RD Service
1.0.11 report disabled server
1.0.10 Fix reconnect for RD Service, Add auto refresh
1.0.9 Fix launch error
1.0.8 bug fixes
1.0.7 Improve high density robots
1.0.6 update interfaces reference
1.0.2 More error catching, fix freerdp login error
1.0.1 Improve sigin process and stability</PackageReleaseNotes>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <PackageTags></PackageTags>
    <Platforms>AnyCPU</Platforms>
    <Authors>Allan Zimmermann</Authors>
    <Company>OpenIAP</Company>
    <!-- 限制资源文件只生成简体中文和英文 -->
    <SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <Target Name="SetNuspecProperties" BeforeTargets="GenerateNuspec">
    <PropertyGroup>
      <NuspecProperties>$(NuspecProperties);id=$(AssemblyName)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);config=$(Configuration)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);version=$(PackageVersion)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);description=$(Description)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);author=$(Authors)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);configurationname=$(ConfigurationName)</NuspecProperties>
    </PropertyGroup>
  </Target>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseNuget|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="FreeRDP-Sharp" Version="0.1.0" />
    <PackageReference Include="SimpleImpersonation" Version="4.2.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.Interfaces\OpenRPA.Interfaces.csproj" />
    <ProjectReference Include="..\OpenRPA.NamedPipeWrapper\OpenRPA.NamedPipeWrapper.csproj" />
    <ProjectReference Include="..\OpenRPA.Net\OpenRPA.Net.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="AxInterop.MSTSCLib">
      <HintPath>lib\AxInterop.MSTSCLib.dll</HintPath>
    </Reference>
    <Reference Include="MSTSCLib">
      <HintPath>lib\MSTSCLib.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <None Update="lib\AxInterop.MSTSCLib.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="lib\MSTSCLib.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
</Project>
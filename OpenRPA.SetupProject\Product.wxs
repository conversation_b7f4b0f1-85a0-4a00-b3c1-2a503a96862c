<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
	<?define VersionNumber="!(bind.FileVersion.sxrpaExe)" ?>
	<!--
   Upgrade code HAS to be the same for all updates.
   Once you've chosen it don't change it.
   -->
	<?define UpgradeCode="c3ca861f-6a0c-40ad-b62d-ef94b84256ec" ?>
	<!-- The URL for add/remove programs -->
	<?define InfoURL="https://openrpa.dk/" ?>
	<?define netver_old="net472" ?>
	<?define netver="net48" ?>
	<!-- 32-bit / 64-bit variables -->
	<?if $(var.Platform) = x64 ?>
	<?define Win64 = "yes" ?>
	<?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
	<?define openrpaExeSourcePath = "$(var.ProjectDir)..\dist\$(var.netver)\SXRPA.exe" ?>
	<?else ?>
	<?define Win64 = "no" ?>
	<?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
	<?define openrpaExeSourcePath = "$(var.ProjectDir)..\dist\$(var.netver)\SXRPA.exe" ?>
	<?endif ?>
	<Product Id="*" Name="!(loc.ApplicationName)" Language="!(loc.Language)" Version="$(var.VersionNumber)" Manufacturer="!(loc.ManufacturerFullName)"
			 UpgradeCode="$(var.UpgradeCode)"  >
		<Package InstallerVersion="200" Compressed="yes"  Platform="x64" />
		<!-- InstallScope="perMachine" -->
		<MajorUpgrade AllowSameVersionUpgrades="yes" DowngradeErrorMessage="A newer version of [ProductName] is already installed." />
		<MediaTemplate EmbedCab="yes" />
		<Icon Id="sxrpa.ico" SourceFile="sxrpa.ico"/>
		<Property Id="ARPPRODUCTICON" Value="sxrpa.ico" />
		<Property Id="INSTALLOFFICEFEATURE" Value="TRUE" />
		<!-- 婧愯矾寰勯厤缃?-->
		<WixVariable Id="MainSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="MainSource2" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="OfficeSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="FormsSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="IESource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="NMSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="ImageSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="JavaSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="ScriptSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="AviRecorderSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="OpenFlowDBSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="RDServicePluginSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="FileWatcherSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>

		<WixVariable Id="PDPluginSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="UtilitiesSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="DatabaseSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>

		<WixVariable Id="PSSource" Overridable="yes" Value="$(var.ProjectDir)..\dist\$(var.netver)"/>
		<WixVariable Id="PythonSource" Overridable="yes" Value="$(var.ProjectDir)..\python_embedded"/>
		<WixVariable Id="PythonVersion" Overridable="yes" Value="$(var.PythonVersion)"/>
		<!--<WixVariable Id="MainSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="MainSource2" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.NativeMessagingHost\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="OfficeSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Office\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="FormsSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Forms\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="IESource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.IE\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="NMSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.NM\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="ImageSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Image\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="JavaSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Java\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="ScriptSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Script\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="AviRecorderSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.AviRecorder\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="OpenFlowDBSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.OpenFlowDB\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="RDServicePluginSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.RDServicePlugin\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="FileWatcherSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.FileWatcher\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="ElisRossumSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Elis.Rossum\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="PDPluginSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.PDPlugin\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="UtilitiesSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Utilities\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="DatabaseSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Database\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="SAPSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.SAP\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="MSSpeechSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.MSSpeech\bin\$(var.Configuration)\$(var.netver)"/>
		<WixVariable Id="PSSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.PS\bin\$(var.Configuration)\$(var.netver)"/>-->
		<!--<WixVariable Id="UpdaterSource" Overridable="yes" Value="$(var.ProjectDir)..\OpenRPA.Updater\bin\$(var.Configuration)\$(var.netver)"/>-->
		<Feature Id="MainFeature" Title="OpenRPA Core Components" Level="1" Absent='disallow' AllowAdvertise='no' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<!--  Display="hidden" -->
			<ComponentGroupRef Id="MainComponents" />
			<ComponentGroupRef Id="UtilitiesComponents" />
			<!--<ComponentGroupRef Id="UpdaterComponents" />-->
			<!--<ComponentGroupRef Id="MainComponentFiles" />-->
		</Feature>
		<Feature Id="OpenFlowDBFeature" Title="OpenFlow specific activities" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="OpenFlowDBComponents" />
		</Feature>
		<Feature Id="PSFeature" Title="PowerShell module mangement tools" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="PSComponents" />
			<!--<ComponentRef Id="PSRegistryEntries"/>-->
		</Feature>
		<Feature Id="OfficeFeature" Title="Microsoft Office Integration" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<Condition Level="0"><![CDATA[NOT(INSTALLOFFICEFEATURE~="TRUE")]]></Condition>
			<ComponentGroupRef Id="OfficeComponents" />
		</Feature>
		<Feature Id="FormsFeature" Title="Forge Forms Integration" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="FormsComponents" />
		</Feature>
		<Feature Id="IEFeature" Title="Internet Explorer Integration" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="IEComponents" />
		</Feature>
		<Feature Id="NMFeature" Title="Chrome/Firefox/Edge Integration" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="NMComponents" />
		</Feature>
		<Feature Id="ImageFeature" Title="Image Recognition and OCR using OpenCV" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="ImageComponents" />
		</Feature>
		<Feature Id="JavaFeature" Title="Java Integration" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="JavaComponents" />
		</Feature>
		<Feature Id="ScriptFeature" Title="Generic Scripting support" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="ScriptComponents" />
		</Feature>
		<Feature Id="PythonFeature" Title="Embedded Python Runtime" Level="1" Absent='disallow' AllowAdvertise='no' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="PythonComponentGroup" />
		</Feature>
		<Feature Id="DatabaseFeature" Title="Microsoft ADO/Database Integration" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="DatabaseComponents" />
		</Feature>

		<Feature Id="FileWatcherFeature" Title="File Watcher run plugin" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="FileWatcherComponents" />
		</Feature>
		<Feature Id="AviRecorderFeature" Title="AviRecord activities and Run plugin" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="AviRecorderComponents" />
		</Feature>

		<Feature Id="RDServicePluginFeature" Title="High Density robots using Remote Desktop" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="RDServicePluginComponents" />
		</Feature>
		<Feature Id="StorageLiteDbFeature" Title="Store/cache data in LiteDB on local filesystem" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="StorageLiteDbComponents" />
		</Feature>
		<Feature Id="StorageFSFeature" Title="Store/cache data as JSon files on local filesystem" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="install" >
			<ComponentGroupRef Id="StorageFSComponents" />
		</Feature>
		<!--<Feature Id="PDPluginFeature" Title="Process Discovery Harvesting Run Plugin" Level="1" Absent='allow' AllowAdvertise='yes' ConfigurableDirectory='INSTALLDIR' TypicalDefault="advertise" >
			<ComponentGroupRef Id="PDPluginComponents" />
		</Feature>-->
		<!--<Property Id="ALLUSERS" Value="2" />-->
		<!--<Property Id="InstallFolder" Value="0" />
    <Property Id='ALLUSERS' Value='2' />
    <Property Id='MSIINSTALLPERUSER' Value='1' />-->
		<Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />
		<UIRef Id="UI_OPENRPA" />
		<DirectoryRef Id="ApplicationProgramsFolder">
			<Component Id="ApplicationShortcut" Guid="$(var.UpgradeCode)" Feature="MainFeature">
				<Shortcut Id="ApplicationStartMenuShortcut"
						  Name="SXRPA"
						  Description="SXRPA"
						  Target="[#sxrpaExe]"
						  WorkingDirectory="INSTALLDIR"/>
				<RemoveFolder Id="CleanUpShortCut" Directory="ApplicationProgramsFolder" On="uninstall"/>
				<RegistryValue Root="HKCU" Key="Software\OpenRPA" Name="installed" Type="integer" Value="1" KeyPath="yes"/>
			</Component>
		</DirectoryRef>
		<!--
    https://stackoverflow.com/questions/43043088/wix-install-feature-conditionally
    https://codingbee.net/wix/wix-the-installation-sequence
    -->
		<Binary Id="OpenRPA.SetupActions" SourceFile="$(var.ProjectDir)..\OpenRPA.SetupActions\dist\OpenRPA.SetupActions.dll" />
		<!--<Binary Id="HasExcelBinary" SourceFile="$(var.OpenRPA.SetupActions.TargetDir)$(var.OpenRPA.SetupActions.TargetName).CA.dll" />-->
		<!--<CustomAction Id="SetProperty" Return="check" Property="FixPaths" Value="[INSTALLDIR]"></CustomAction>-->
		<!--<CustomAction Id="FixPaths" BinaryKey="HasExcelBinary" DllEntry="FixPaths" Execute="deferred" Impersonate="no" />-->
		<!--<CustomAction Id="HasExcel" BinaryKey="HasExcelBinary" DllEntry="HasExcel" Execute="immediate" Return="check" />-->
		<!--<CustomAction Id="RunFindRelatedProducts" BinaryKey="HasExcelBinary" DllEntry="RunFindRelatedProducts" Execute="immediate" Return="check" />-->
		<!--<CustomAction Id="FixPaths"
					BinaryKey="HasExcelBinary"
					DllEntry="FixPaths"
					Execute="deferred"
					Return="check"
					Impersonate="no" />-->
		<CustomAction Id="SetCustomActionDataValue" Return="check"
					 Property="FixPaths" Value="INSTALLDIR=[INSTALLDIR]" />
		<CustomAction Id="test_command2" Script="vbscript">
			<![CDATA[          
          MsgBox(Session.Property("INSTALLOFFICEFEATURE"))
      ]]>
		</CustomAction>
		<!--<InstallUISequence>
			<Custom Action='HasExcel'  Before='AppSearch' />
			<Custom Action="test_command2" After="HasExcel">NOT Installed</Custom>
		</InstallUISequence>
		<InstallExecuteSequence>
			<Custom Action="SetCustomActionDataValue" Before="FixPaths" />
			<Custom Action="FixPaths" Before="InstallFinalize" />
		</InstallExecuteSequence>-->
		<!-- 
    https://wixtoolset.org/documentation/manual/v3/xsd/wix/installuisequence.html
    https://wixtoolset.org/documentation/manual/v3/xsd/wix/installexecutesequence.html
    FindRelatedProducts
    AppSearch
    -->
		<!--<InstallUISequence>
			<FindRelatedProducts>0</FindRelatedProducts>
			<Custom Action="RunFindRelatedProducts" Before='FindRelatedProducts'>NOT Installed</Custom>
		</InstallUISequence>
		<InstallExecuteSequence>
			<FindRelatedProducts>0</FindRelatedProducts>
			<Custom Action="RunFindRelatedProducts" Before='FindRelatedProducts'>NOT Installed</Custom>
		</InstallExecuteSequence>-->
	</Product>
	<Fragment>
		<Directory Id="TARGETDIR" Name="SourceDir">
			<Directory Id="LocalAppDataFolder">
				<!--<Directory Id="INSTALLDIR2" Name="OpenRPA" />-->
				<!--<Component Id="MainFeature" Guid="*">
        </Component>-->
			</Directory>
			<Directory Id="ProgramFiles64Folder">
				<Directory Id="INSTALLDIR" Name="SXRPA" />
			</Directory>
			<Directory Id="ProgramMenuFolder">
				<Directory Id="ApplicationProgramsFolder" Name="SXRPA"/>
			</Directory>
		</Directory>
	</Fragment>
</Wix>


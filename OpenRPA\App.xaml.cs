using OpenRPA.Interfaces;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace OpenRPA
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application, ISingleInstanceApp
    {
        public static Views.SplashWindow splash = null;
        [STAThread]
        public static void Main()
        {
            if (SingleInstance<App>.InitializeAsFirstInstance("OpenRPA"))
            {
                AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
                // AppDomain.CurrentDomain.FirstChanceException += CurrentDomain_FirstChanceHandler;
                try
                {
                    var args = Environment.GetCommandLineArgs();
                    CommandLineParser parser = new CommandLineParser();
                    // parser.Parse(string.Join(" ", args), true);
                    var options = parser.Parse(args, true);
                    if (options.ContainsKey("workingdir"))
                    {
                        var filepath = options["workingdir"].ToString();
                        if (System.IO.Directory.Exists(filepath))
                        {
                            Log.ResetLogPath(filepath);
                        }
                        else
                        {
                            MessageBox.Show("Path not found " + filepath);
                            return;
                        }
                    }
                }
                catch (Exception)
                {
                }
                var application = new App();
                application.InitializeComponent();
                application.Run();
                // application.Run();
                // Allow single instance code to perform cleanup operations
                SingleInstance<App>.Cleanup();
            }
        }
        static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs args)
        {
            Log.Function("MainWindow", "CurrentDomain_UnhandledException");
            try
            {
                Exception ex = (Exception)args.ExceptionObject;
                Log.Error(ex.ToString());
                Log.Error("MyHandler caught : " + ex.Message);
                Log.Error("Runtime terminating: {0}", (args.IsTerminating).ToString());
            }
            catch (Exception)
            {
            }
        }
        public static System.Windows.Forms.NotifyIcon notifyIcon { get; set; } = new System.Windows.Forms.NotifyIcon();
        public App()
        {
            // 配置TLS协议以支持Win7连接现代服务器
            try
            {
                System.Net.ServicePointManager.SecurityProtocol =
                    System.Net.SecurityProtocolType.Tls12 |
                    System.Net.SecurityProtocolType.Tls11 |
                    System.Net.SecurityProtocolType.Tls;
                Log.Information("TLS协议已配置为支持Win7兼容性");
            }
            catch (Exception ex)
            {
                Log.Warning($"TLS协议配置失败: {ex.Message}");
            }

            if (!string.IsNullOrEmpty(Config.local.culture))
            {
                try
                {
                    var cultur = System.Globalization.CultureInfo.GetCultureInfo(Config.local.culture);
                    System.Threading.Thread.CurrentThread.CurrentUICulture = cultur;
                    System.Globalization.CultureInfo.DefaultThreadCurrentCulture = cultur;
                    System.Globalization.CultureInfo.DefaultThreadCurrentUICulture = cultur;
                    ProcessThreadCollection currentThreads = Process.GetCurrentProcess().Threads;
                    foreach (object obj in currentThreads)
                    {
                        try
                        {
                            Thread t = obj as Thread;
                            if (t != null)
                            {
                                t.CurrentUICulture = cultur;
                                t.CurrentCulture = cultur;
                            }
                        }
                        catch (Exception)
                        {
                        }
                    }


                }
                catch (Exception)
                {
                }
            }
            AppDomain.CurrentDomain.AssemblyResolve += new ResolveEventHandler(LoadFromSameFolder);
            try
            {
                // Try multiple possible resource URIs for the icon
                Stream iconStream = null;
                string[] possibleUris = {
                    "pack://application:,,,/SXRPA;component/Resources/sxrpa.ico",
                    "/SXRPA;component/Resources/sxrpa.ico",
                };

                foreach (string uriString in possibleUris)
                {
                    try
                    {
                        var resourceInfo = System.Windows.Application.GetResourceStream(new Uri(uriString, UriKind.RelativeOrAbsolute));
                        if (resourceInfo != null)
                        {
                            iconStream = resourceInfo.Stream;
                            Log.Debug($"Successfully loaded icon from: {uriString}");
                            break;
                        }
                    }
                    catch (Exception uriEx)
                    {
                        Log.Debug($"Failed to load icon from {uriString}: {uriEx.Message}");
                    }
                }

                if (iconStream != null)
                {
                    notifyIcon.Icon = new System.Drawing.Icon(iconStream);
                    notifyIcon.Visible = false;
                    notifyIcon.Text = "SXRPA";
                    // 设置通知栏图标永远显示，不自动隐藏
                    notifyIcon.BalloonTipIcon = System.Windows.Forms.ToolTipIcon.Info;
                    //notifyIcon.ShowBalloonTip(5000, "Title", "Text", System.Windows.Forms.ToolTipIcon.Info);

                    // 使用MouseClick事件来区分左键和右键点击
                    notifyIcon.MouseClick += nIcon_MouseClick;
                    notifyIcon.DoubleClick += nIcon_Click;

                    // 创建右键菜单
                    var contextMenu = new System.Windows.Forms.ContextMenuStrip();
                    var showMenuItem = new System.Windows.Forms.ToolStripMenuItem("显示主窗口");
                    var exitMenuItem = new System.Windows.Forms.ToolStripMenuItem("退出应用");

                    showMenuItem.Click += (s, e) => nIcon_Click(s, e);
                    exitMenuItem.Click += (s, e) => ExitApplication();

                    contextMenu.Items.Add(showMenuItem);
                    contextMenu.Items.Add(new System.Windows.Forms.ToolStripSeparator());
                    contextMenu.Items.Add(exitMenuItem);

                    notifyIcon.ContextMenuStrip = contextMenu;
                }
                else
                {
                    Log.Warning("Could not load application icon from any known resource URI");
                    // 创建一个默认图标，确保通知栏图标能正常工作
                    try
                    {
                        // 创建一个简单的默认图标
                        var bitmap = new System.Drawing.Bitmap(16, 16);
                        using (var g = System.Drawing.Graphics.FromImage(bitmap))
                        {
                            g.Clear(System.Drawing.Color.Blue);
                            g.FillEllipse(System.Drawing.Brushes.White, 2, 2, 12, 12);
                        }
                        notifyIcon.Icon = System.Drawing.Icon.FromHandle(bitmap.GetHicon());
                        Log.Information("Created default icon for system tray");
                    }
                    catch (Exception iconEx)
                    {
                        Log.Error($"Failed to create default icon: {iconEx.Message}");
                        // 最后的备选方案：使用系统默认图标
                        try
                        {
                            notifyIcon.Icon = System.Drawing.SystemIcons.Application;
                            Log.Information("Using system default application icon");
                        }
                        catch (Exception sysIconEx)
                        {
                            Log.Error($"Failed to use system icon: {sysIconEx.Message}");
                        }
                    }

                    notifyIcon.Visible = false;
                    notifyIcon.Text = "SXRPA";

                    // 使用MouseClick事件来区分左键和右键点击
                    notifyIcon.MouseClick += nIcon_MouseClick;
                    notifyIcon.DoubleClick += nIcon_Click;

                    // 即使没有图标也创建右键菜单
                    var contextMenu = new System.Windows.Forms.ContextMenuStrip();
                    var showMenuItem = new System.Windows.Forms.ToolStripMenuItem("显示主窗口");
                    var exitMenuItem = new System.Windows.Forms.ToolStripMenuItem("退出应用");

                    showMenuItem.Click += (s, e) => nIcon_Click(s, e);
                    exitMenuItem.Click += (s, e) => ExitApplication();

                    contextMenu.Items.Add(showMenuItem);
                    contextMenu.Items.Add(new System.Windows.Forms.ToolStripSeparator());
                    contextMenu.Items.Add(exitMenuItem);

                    notifyIcon.ContextMenuStrip = contextMenu;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error setting up notification icon: {ex}");
            }
        }
        private static void CopyFilesRecursively(DirectoryInfo source, DirectoryInfo target)
        {
            foreach (DirectoryInfo dir in source.GetDirectories())
            {
                CopyFilesRecursively(dir, target.CreateSubdirectory(dir.Name));
            }

            foreach (FileInfo file in source.GetFiles())
            {
                file.CopyTo(System.IO.Path.Combine(target.FullName, file.Name));
            }
        }
        static Assembly LoadFromSameFolder(object sender, ResolveEventArgs args)
        {
            string assemblyPath = "";
            if (args != null && !string.IsNullOrEmpty(args.Name)) assemblyPath = args.Name;
            try
            {
                assemblyPath = new AssemblyName(args.Name).Name + ".dll";
            }
            catch (Exception)
            {
            }
            try
            {
                // 处理程序集名称重定向：OpenRPA -> SXRPA
                string requestedAssemblyName = new AssemblyName(args.Name).Name;
                if (requestedAssemblyName == "OpenRPA")
                {
                    // 返回当前执行的程序集（SXRPA.exe）
                    return Assembly.GetExecutingAssembly();
                }

                if (args.Name.StartsWith("CefSharp"))
                {
                    string assemblyName = args.Name.Split(new[] { ',' }, 2)[0] + ".dll";
                    string archSpecificPath = System.IO.Path.Combine(AppDomain.CurrentDomain.SetupInformation.ApplicationBase,
                                                           Environment.Is64BitProcess ? "x64" : "x86",
                                                           assemblyName);

                    return File.Exists(archSpecificPath)
                               ? Assembly.LoadFile(archSpecificPath)
                               : null;
                }

                string folderPath = System.IO.Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                assemblyPath = System.IO.Path.Combine(folderPath, requestedAssemblyName + ".dll");
                if (System.IO.File.Exists(assemblyPath)) return Assembly.LoadFrom(assemblyPath);

                folderPath = Interfaces.Extensions.PluginsDirectory;
                assemblyPath = System.IO.Path.Combine(folderPath, requestedAssemblyName + ".dll");
                if (System.IO.File.Exists(assemblyPath)) return Assembly.LoadFrom(assemblyPath);

                folderPath = Path.Combine(Interfaces.Extensions.ProjectsDirectory, "extensions");
                assemblyPath = System.IO.Path.Combine(folderPath, requestedAssemblyName + ".dll");
                if (System.IO.File.Exists(assemblyPath)) return Assembly.LoadFrom(assemblyPath);

                folderPath = System.IO.Path.GetTempPath();
                assemblyPath = System.IO.Path.Combine(folderPath, requestedAssemblyName + ".dll");
                if (System.IO.File.Exists(assemblyPath)) return Assembly.LoadFrom(assemblyPath);
            }
            catch (Exception ex)
            {
                // 获取调用堆栈信息用于调试
                var stackTrace = new System.Diagnostics.StackTrace(true);
                var callingMethod = stackTrace.GetFrame(2)?.GetMethod();
                var callingAssembly = callingMethod?.DeclaringType?.Assembly?.GetName()?.Name ?? "Unknown";
                var callingType = callingMethod?.DeclaringType?.FullName ?? "Unknown";
                var callingMethodName = callingMethod?.Name ?? "Unknown";

                Log.Error($"=== ASSEMBLY RESOLUTION FAILED ===");
                Log.Error($"Requested Assembly: {args.Name}");
                Log.Error($"Requesting Assembly: {callingAssembly}");
                Log.Error($"Requesting Type: {callingType}");
                Log.Error($"Requesting Method: {callingMethodName}");
                Log.Error($"Last attempted path: {assemblyPath}");
                Log.Error($"Exception: {ex}");
                Log.Error($"=== END ASSEMBLY RESOLUTION FAILED ===");
            }
            return null;
        }
        void nIcon_Click(object sender, EventArgs e)
        {
            GenericTools.Restore();
        }

        void nIcon_MouseClick(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            // 只有左键点击才显示窗口，右键点击会自动显示上下文菜单
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                GenericTools.Restore();
            }
        }

        void ExitApplication()
        {
            try
            {
                // 获取主窗口并真正关闭
                if (GenericTools.MainWindow is MainWindow mainWindow)
                {
                    mainWindow.ReallyClose();
                }
                else if (GenericTools.MainWindow is AgentWindow agentWindow)
                {
                    agentWindow.ReallyClose();
                }
                else
                {
                    // 如果无法获取主窗口，直接关闭应用程序
                    Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                Log.Error($"Error exiting application: {ex}");
                Current.Shutdown();
            }
        }
        private void Application_Exit(object sender, ExitEventArgs e)
        {
            // 清理启动窗口
            if (splash != null)
            {
                try
                {
                    splash.Hide();
                    splash = null;
                }
                catch (Exception ex)
                {
                    Log.Error($"Error disposing splash window: {ex}");
                }
            }

            // 清理通知图标
            if (notifyIcon != null)
            {
                if (notifyIcon.Icon != null) notifyIcon.Icon.Dispose();
                notifyIcon.Dispose();
            }
        }
        public bool SignalExternalCommandLineArgs(IList<string> args)
        {
            nIcon_Click(null, null);
            RobotInstance.instance.ParseCommandLineArgs(args);
            return true;
        }
        private async void Application_Startup(object sender, StartupEventArgs e)
        {
            try
            {
                AutomationHelper.syncContext = System.Threading.SynchronizationContext.Current;
                System.Threading.Thread.CurrentThread.Name = "UIThread";

                bool isAgent = false;
                try
                {
                    // Test ProjectsDirectory access first

                    var projectsDir = Interfaces.Extensions.ProjectsDirectory;

                    // Now test Config.local access
                    isAgent = Config.local.isagent;

                    // Show splash screen if enabled (after Config is initialized)
                    try
                    {
                        if (Config.local.showloadingscreen)
                        {
                            splash = new Views.SplashWindow();
                            // 确保启动画面不是主窗口，也不影响应用程序关闭
                            splash.Owner = null;
                            splash.ShowInTaskbar = false;
                            // 确保启动窗口不会成为主窗口
                            splash.WindowStartupLocation = WindowStartupLocation.CenterScreen;

                            // 重要：先设置ShutdownMode，防止splash成为主窗口时影响应用程序关闭
                            this.ShutdownMode = ShutdownMode.OnExplicitShutdown;

                            splash.Show();
                            splash.UpdateStatus("正在初始化...");
                        }
                    }
                    catch (Exception splashEx)
                    {
                        Log.Error($"Failed to show splash screen: {splashEx}");
                    }
                }
                catch (Exception configEx)
                {
                    System.Diagnostics.Trace.WriteLine($"Failed to access Config.local: {configEx}");

                    // Try to get more detailed error information
                    try
                    {
                        System.Diagnostics.Trace.WriteLine($"Settings file path: {Config.SettingsFile}");
                        System.Diagnostics.Trace.WriteLine($"ProjectsDirectory attempt: {Interfaces.Extensions.ProjectsDirectory}");
                    }
                    catch (Exception innerEx)
                    {
                        System.Diagnostics.Trace.WriteLine($"Additional error getting paths: {innerEx}");
                    }

                    // Only show MessageBox in interactive mode
                    if (Environment.UserInteractive && !Environment.GetCommandLineArgs().Any(arg => arg.Contains("msiexec") || arg.Contains("/quiet") || arg.Contains("/silent")))
                    {
                        System.Windows.MessageBox.Show($"Configuration error: {configEx.Message}\n\nPlease check if the application has proper permissions to access the configuration directory.\n\nThe application will continue with default settings.", "SXRPA Configuration Warning", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    }

                    // Don't shutdown, continue with default configuration
                    isAgent = false; // Default value
                }
                // Update splash status
                if (splash != null) splash.UpdateStatus("正在设置启动窗口...");

                // 不再使用StartupUri，窗口将由RobotInstance.CreateMainWindow创建
                Log.Debug($"Window creation will be handled by RobotInstance.CreateMainWindow, isAgent = {isAgent}");

                notifyIcon.Visible = isAgent;
                if (Config.local.files_pending_deletion.Length > 0)
                {
                    bool sucess = true;
                    foreach (var f in Config.local.files_pending_deletion)
                    {
                        try
                        {
                            if (System.IO.File.Exists(f)) System.IO.File.Delete(f);
                        }
                        catch (Exception ex)
                        {
                            sucess = false;
                            Log.Error(ex.ToString());
                        }
                    }
                    if (sucess)
                    {
                        Config.local.files_pending_deletion = new string[] { };
                        Config.Save();
                    }
                }

                if (Config.local.restore_dependencies_on_startup)
                {
                    Log.Debug("Package restore on startup enabled -> cleaning existing extensions.");
                    var extensionsPath = Path.Combine(Interfaces.Extensions.ProjectsDirectory, "extensions");
                    try
                    {
                        // 确保extensions目录存在
                        if (!Directory.Exists(extensionsPath))
                        {
                            Directory.CreateDirectory(extensionsPath);
                            Log.Debug($"Created extensions directory: {extensionsPath}");
                        }

                        if (Directory.Exists(extensionsPath))
                        {
                            foreach (var file in Directory.GetFiles(extensionsPath))
                            {
                                try
                                {
                                    File.Delete(file);
                                }
                                catch (Exception ex)
                                {
                                    Log.Error("Could not clean extension: " + ex.ToString());
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"Could not create or access extensions directory {extensionsPath}: {ex}");
                    }
                }

                RobotInstance.instance.Status += App_Status;
                Input.InputDriver.Instance.initCancelKey(Config.local.cancelkey);
                Plugins.LoadPlugins(RobotInstance.instance, Interfaces.Extensions.PluginsDirectory, false);
                RobotInstance.instance.Initialize();
            }
            catch (Exception ex)
            {
                // Write to console and debug output first, in case Log is not working
                System.Diagnostics.Trace.WriteLine($"=== Application_Startup Exception ===");
                System.Diagnostics.Trace.WriteLine($"Exception Type: {ex.GetType().FullName}");
                System.Diagnostics.Trace.WriteLine($"Exception Message: {ex.Message}");
                System.Diagnostics.Trace.WriteLine($"Stack Trace: {ex.StackTrace}");
                Console.WriteLine($"=== Application_Startup Exception ===");
                Console.WriteLine($"Exception Type: {ex.GetType().FullName}");
                Console.WriteLine($"Exception Message: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");

                try
                {
                    Log.Error($"=== Application_Startup Exception ===");
                    Log.Error($"Exception Type: {ex.GetType().FullName}");
                    Log.Error($"Exception Message: {ex.Message}");
                    Log.Error($"Stack Trace: {ex.StackTrace}");
                    if (ex.InnerException != null)
                    {
                        Log.Error($"Inner Exception: {ex.InnerException}");
                    }
                    Log.Error(ex.ToString());
                }
                catch
                {
                    // If logging fails, continue with console output
                }

                string errorMessage = $"Application startup failed:\n\n{ex.Message}";
                if (ex is System.IO.FileNotFoundException fileEx)
                {
                    errorMessage += $"\n\nMissing file: {fileEx.FileName}";
                    if (!string.IsNullOrEmpty(fileEx.FusionLog))
                    {
                        errorMessage += $"\n\nFusion Log:\n{fileEx.FusionLog}";
                    }
                }

                errorMessage += $"\n\nFull Exception Details:\n{ex}";

                MessageBox.Show(errorMessage, "SXRPA Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown(1);
            }

            await Task.Run(async () =>
            {
                try
                {
                    if (splash != null) splash.UpdateStatus("正在加载插件...");
                    // Plugins.LoadPlugins(RobotInstance.instance, Interfaces.Extensions.ProjectsDirectory);

                    if (splash != null) splash.UpdateStatus("正在初始化主窗口...");
                    await RobotInstance.instance.init();

                    if (splash != null) splash.UpdateStatus("启动完成");

                    // 启动画面将由主窗口的 Window_Loaded 事件关闭
                }
                catch (Exception ex)
                {
                    Log.Error(ex.ToString());
                    Console.WriteLine(ex.ToString());

                    // 关闭启动画面
                    if (splash != null)
                    {
                        await Dispatcher.InvokeAsync(() =>
                        {
                            splash?.CloseSplash();
                            splash = null;
                        });
                    }

                    MessageBox.Show(ex.Message);
                }
            });
        }
        private void App_Status(string message)
        {
            try
            {
                Log.Debug(message);
                // notifyIcon.ShowBalloonTip(5000, "Title", message, System.Windows.Forms.ToolTipIcon.Info);
                if (splash != null) splash.UpdateStatus(message);
            }
            catch (Exception)
            {
            }
        }
    }
}

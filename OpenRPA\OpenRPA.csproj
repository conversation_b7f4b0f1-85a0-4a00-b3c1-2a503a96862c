<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<!--<CefSharpTargetDir>cef</CefSharpTargetDir>-->
		<CefSharpAnyCpuSupport>true</CefSharpAnyCpuSupport>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net48</TargetFramework>
		<UseWPF>true</UseWPF>
		<UseWindowsForms>true</UseWindowsForms>
		<ApplicationIcon>Resources\sxrpa.ico</ApplicationIcon>
		<StartupObject />
		<AssemblyName>SXRPA</AssemblyName>
		<GeneratePackageOnBuild>false</GeneratePackageOnBuild>
		<EnableDefaultPageItems>false</EnableDefaultPageItems>
		<EnableDefaultApplicationDefinition>false</EnableDefaultApplicationDefinition>
		<!--<NuspecFile>nuget.nuspec</NuspecFile>-->
		<Description>Base UI of SXRPA, used as part of SXRPA robot</Description>
		<PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
		<PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
		<Version>*********</Version>
		<PackageReleaseNotes></PackageReleaseNotes>
		<PackageIcon>openrpa.png</PackageIcon>
		<Configurations>Debug;Release;ReleaseNuget;PrepInstaller</Configurations>
		<PackageTags></PackageTags>
		<RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
		<Platforms>AnyCPU</Platforms>
		<Authors>Allan Zimmermann</Authors>
		<Company>OpenIAP</Company>
		<UserSecretsId>67d03aca-cfb1-4f1c-9efa-111ce7476743</UserSecretsId>
		<!-- 限制资源文件只生成简体中文和英文 -->
		<SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>..\debug</OutputPath>
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<OutputPath>..\dist</OutputPath>
		<PlatformTarget>x64</PlatformTarget>
		<Optimize>False</Optimize>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
		<OutputPath></OutputPath>
		<PlatformTarget>AnyCPU</PlatformTarget>
	</PropertyGroup>

	<Target Name="SetNuspecProperties" BeforeTargets="GenerateNuspec">
		<PropertyGroup>
			<NuspecProperties>$(NuspecProperties);id=$(AssemblyName)</NuspecProperties>
			<NuspecProperties>$(NuspecProperties);config=$(Configuration)</NuspecProperties>
			<NuspecProperties>$(NuspecProperties);version=$(PackageVersion)</NuspecProperties>
			<NuspecProperties>$(NuspecProperties);description=$(Description)</NuspecProperties>
			<NuspecProperties>$(NuspecProperties);author=$(Authors)</NuspecProperties>
			<NuspecProperties>$(NuspecProperties);configurationname=$(ConfigurationName)</NuspecProperties>
		</PropertyGroup>
	</Target>
	<ItemGroup>
		<!-- https://tyrrrz.me/blog/additional-nuget-files-in-new-csproj -->
		<None Include="$(OutputPath)\SXRPA.exe.config" Pack="true" PackagePath="build" />
		<None Include="Resources\openrpa.png" Pack="true" PackagePath="\" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="DotNetProjects.Wpf.Toolkit" Version="5.0.43" />
		<PackageReference Include="Extended.Wpf.Toolkit" Version="4.2.0" />
		<PackageReference Include="Microsoft.Bcl.HashCode" Version="1.1.1" />
		<PackageReference Include="NuGet.Common" Version="6.11.1" />
		<PackageReference Include="NuGet.Packaging" Version="6.11.1" />
		<PackageReference Include="NuGet.Protocol" Version="6.11.1" />
		<PackageReference Include="NuGet.Resolver" Version="6.11.1" />
		<PackageReference Include="NuGet.Versioning" Version="6.11.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.1" />
		<PackageReference Include="System.Drawing.Common" Version="4.7.0" />
		<PackageReference Include="System.Management" Version="4.7.0" />
		<PackageReference Include="System.ServiceProcess.ServiceController" Version="4.7.0" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="4.7.0" />
		<PackageReference Include="WindowsAPICodePack-Core" Version="1.1.2" />
		<PackageReference Include="WindowsAPICodePack-Shell" Version="1.1.1" />
		<!-- 显式引用以确保正确版本 -->
		<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.0" />
		<PackageReference Include="System.Memory" Version="4.5.5" />
		<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
	</ItemGroup>
	<ItemGroup>
		<None Remove="Activities\AddWorkitemDesigner.xaml" />
		<None Remove="Activities\BreakableDoWhileDesigner.xaml" />
		<None Remove="Activities\BreakableWhileDesigner.xaml" />
		<None Remove="Activities\BreakDesigner.xaml" />
		<None Remove="Activities\ClickElementDesigner.xaml" />
		<None Remove="Activities\CloseApplicationDesigner.xaml" />
		<None Remove="Activities\CommentOutDesigner.xaml" />
		<None Remove="Activities\ContinueDesigner.xaml" />
		<None Remove="Activities\CopyClipboardDesigner.xaml" />
		<None Remove="Activities\DeleteWorkitemDesigner.xaml" />
		<None Remove="Activities\DetectorDesigner.xaml" />
		<None Remove="Activities\FocusElementDesigner.xaml" />
		<None Remove="Activities\ForEachDataRowDesigner.xaml" />
		<None Remove="Activities\ForEachOfDesigner.xaml" />
		<None Remove="Activities\GetWorkflowInstanceDesigner.xaml" />
		<None Remove="Activities\HighlightElementDesigner.xaml" />
		<None Remove="Activities\InsertClipboardDesigner.xaml" />
		<None Remove="Activities\InvokeOpenFlowDesigner.xaml" />
		<None Remove="Activities\InvokeOpenRPADesigner.xaml" />
		<None Remove="Activities\InvokeRemoteOpenRPADesigner.xaml" />
		<None Remove="Activities\MoveElementDesigner.xaml" />
		<None Remove="Activities\MoveMouseDesigner.xaml" />
		<None Remove="Activities\OpenApplicationDesigner.xaml" />
		<None Remove="Activities\PopWorkitemDesigner.xaml" />
		<None Remove="Activities\ShowBalloonTipDesigner.xaml" />
		<None Remove="Activities\StopOpenRPADesigner.xaml" />
		<None Remove="Activities\TypeTextDesigner.xaml" />
		<None Remove="Activities\UpdateWorkitemDesigner.xaml" />
		<None Remove="Activities\Workitems\BulkAddWorkitemsDesigner.xaml" />
		<None Remove="Activities\Workitems\ThrowBusinessRuleExceptionDesigner.xaml" />
		<None Remove="AgentWindow.xaml" />
		<None Remove="App.xaml" />
		<None Remove="Images\PinDocMenu_Black.png" />
		<None Remove="MainWindow.xaml" />
		<None Remove="Resources\Buttons\Button.png" />
		<None Remove="Resources\Buttons\Copy.png" />
		<None Remove="Resources\Buttons\Delete.png" />
		<None Remove="Resources\Buttons\Exit.png" />
		<None Remove="Resources\Buttons\Export.png" />
		<None Remove="Resources\Buttons\Import.png" />
		<None Remove="Resources\Buttons\New.png" />
		<None Remove="Resources\Buttons\Open.png" />
		<None Remove="Resources\Buttons\OpenChrome.png" />
		<None Remove="Resources\Buttons\OpenEdge.png" />
		<None Remove="Resources\Buttons\OpenFirefox.png" />
		<None Remove="Resources\Buttons\Permissions.png" />
		<None Remove="Resources\Buttons\picture_in_picture.png" />
		<None Remove="Resources\Buttons\Play.png" />
		<None Remove="Resources\Buttons\Record.png" />
		<None Remove="Resources\Buttons\Reload.png" />
		<None Remove="Resources\Buttons\Revert.png" />
		<None Remove="Resources\Buttons\Save.png" />
		<None Remove="Resources\Buttons\Signout.png" />
		<None Remove="Resources\Buttons\Stop.png" />
		<None Remove="Resources\Buttons\swap.png" />
		<None Remove="Resources\close.png" />
		<None Remove="Resources\designer\addworkitem.png" />
		<None Remove="Resources\designer\break.png" />
		<None Remove="Resources\designer\bulkaddworkitems.png" />
		<None Remove="Resources\designer\closeapplication.png" />
		<None Remove="Resources\designer\commentout.png" />
		<None Remove="Resources\designer\continue.png" />
		<None Remove="Resources\designer\copyclipboard.png" />
		<None Remove="Resources\designer\deleteworkitem.png" />
		<None Remove="Resources\designer\detector.png" />
		<None Remove="Resources\designer\dowhile.png" />
		<None Remove="Resources\designer\elementclick.png" />
		<None Remove="Resources\designer\focuselement.png" />
		<None Remove="Resources\designer\foreach.png" />
		<None Remove="Resources\designer\getworkflowinstance.png" />
		<None Remove="Resources\designer\highlightelement.png" />
		<None Remove="Resources\designer\insertclipboard.png" />
		<None Remove="Resources\designer\invokeopenflow.png" />
		<None Remove="Resources\designer\invokeremoterpaworkflow.png" />
		<None Remove="Resources\designer\invokerpaworkflow.png" />
		<None Remove="Resources\designer\mouse.png" />
		<None Remove="Resources\designer\moveelement.png" />
		<None Remove="Resources\designer\openapplication.png" />
		<None Remove="Resources\designer\popworkitem.png" />
		<None Remove="Resources\designer\showballontip.png" />
		<None Remove="Resources\designer\stopopnerpa.png" />
		<None Remove="Resources\designer\throwbusinessruleexception.png" />
		<None Remove="Resources\designer\typetext.png" />
		<None Remove="Resources\designer\updateworkitem.png" />
		<None Remove="Resources\designer\while.png" />
		<None Remove="Resources\icons\activity.png" />
		<None Remove="Resources\icons\flowchart.png" />
		<None Remove="Resources\icons\openin.png" />
		<None Remove="Resources\icons\property.png" />
		<None Remove="Resources\icons\spinner2.png" />
		<None Remove="Resources\icons\variable.png" />
		<None Remove="Resources\loadingbackground.jpg" />
		<None Remove="Resources\open.png" />
		<None Remove="Resources\open_rpa.ico" />
		<None Remove="Resources\open_rpa_white.png" />
		<None Remove="Resources\snippet.png" />
		<None Remove="Resources\state\Abort.png" />
		<None Remove="Resources\state\Completed.png" />
		<None Remove="Resources\state\failed.png" />
		<None Remove="Resources\state\risk.png" />
		<None Remove="Resources\state\Running.png" />
		<None Remove="Resources\state\Running_green.png" />
		<None Remove="Resources\state\sensor.png" />
		<None Remove="Resources\state\unloaded.png" />
		<None Remove="Resources\state\workitemqueue.png" />
		<None Remove="Resources\toolbox\addworkitem.png" />
		<None Remove="Resources\toolbox\break.png" />
		<None Remove="Resources\toolbox\bulkaddworkitems.png" />
		<None Remove="Resources\toolbox\closeapplication.png" />
		<None Remove="Resources\toolbox\commentout.png" />
		<None Remove="Resources\toolbox\continue.png" />
		<None Remove="Resources\toolbox\copyclipboard.png" />
		<None Remove="Resources\toolbox\deleteworkitem.png" />
		<None Remove="Resources\toolbox\detector.png" />
		<None Remove="Resources\toolbox\dowhile.png" />
		<None Remove="Resources\toolbox\elementclick.png" />
		<None Remove="Resources\toolbox\focuselement.png" />
		<None Remove="Resources\toolbox\foreach.png" />
		<None Remove="Resources\toolbox\getworkflowinstance.png" />
		<None Remove="Resources\toolbox\highlightelement.png" />
		<None Remove="Resources\toolbox\insertclipboard.png" />
		<None Remove="Resources\toolbox\invokeopenflow.png" />
		<None Remove="Resources\toolbox\invokeremoterpaworkflow.png" />
		<None Remove="Resources\toolbox\invokerpaworkflow.png" />
		<None Remove="Resources\toolbox\mouse.png" />
		<None Remove="Resources\toolbox\moveelement.png" />
		<None Remove="Resources\toolbox\openapplication.png" />
		<None Remove="Resources\toolbox\popworkitem.png" />
		<None Remove="Resources\toolbox\showballontip.png" />
		<None Remove="Resources\toolbox\stopopnerpa.png" />
		<None Remove="Resources\toolbox\throwbusinessruleexception.png" />
		<None Remove="Resources\toolbox\typetext.png" />
		<None Remove="Resources\toolbox\updateworkitem.png" />
		<None Remove="Resources\toolbox\while.png" />
		<None Remove="Views\AgentViewProjects.xaml" />
		<None Remove="Views\ChildSession.xaml" />
		<None Remove="Views\CloseableHeader.xaml" />
		<None Remove="Views\DetectorsView.xaml" />
		<None Remove="Views\EditXAML.xaml" />
		<None Remove="Views\InsertSelect.xaml" />
		<None Remove="Views\InsertText.xaml" />
		<None Remove="Views\KeyboardSeqWindow.xaml" />
		<None Remove="Views\OpenProject.xaml" />
		<None Remove="Views\PackageManager.xaml" />
		<None Remove="Views\PendingToken.xaml" />
		<None Remove="Views\PermissionsWindow.xaml" />
		<None Remove="Views\RecorderPlugins.xaml" />
		<None Remove="Views\RunPlugins.xaml" />
		<None Remove="Views\selectUserWindow.xaml" />
		<None Remove="Views\showVariables.xaml" />
		<None Remove="Views\Snippets.xaml" />
		<None Remove="Views\SplashWindow.xaml" />
		<None Remove="Views\WFDesigner.xaml" />
		<None Remove="Views\wfToolbox.xaml" />
		<None Remove="Views\WorkflowInstances.xaml" />
		<None Remove="Views\WorkitemQueue.xaml" />
		<None Remove="Views\WorkItemQueuesView.xaml" />
	</ItemGroup>
	<ItemGroup>
		<Page Include="Activities\Workitems\ThrowBusinessRuleExceptionDesigner.xaml">
		  <SubType>Designer</SubType>
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\Workitems\BulkAddWorkitemsDesigner.xaml">
		  <SubType>Designer</SubType>
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\Workitems\AddWorkitemDesigner.xaml">
		  <SubType>Designer</SubType>
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\BreakableDoWhileDesigner.xaml">
			<SubType>Designer</SubType>
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\Workitems\DeleteWorkitemDesigner.xaml">
		  <SubType>Designer</SubType>
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\ForEachOfDesigner.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\ContinueDesigner.xaml">
			<SubType>Designer</SubType>
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\BreakDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\GetWorkflowInstanceDesigner.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\MoveElementDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\FocusElementDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\BreakableWhileDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\Workitems\PopWorkitemDesigner.xaml">
		  <SubType>Designer</SubType>
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\ShowBalloonTipDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\MoveMouseDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\ClickElementDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\CloseApplicationDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\CommentOutDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\CopyClipboardDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\DetectorDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\ForEachDataRowDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\HighlightElementDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\InsertClipboardDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\InvokeOpenFlowDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\InvokeRemoteOpenRPADesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\InvokeOpenRPADesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\OpenApplicationDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\StopOpenRPADesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\TypeTextDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Activities\Workitems\UpdateWorkitemDesigner.xaml">
		  <SubType>Designer</SubType>
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="App.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="AgentWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\AgentViewProjects.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\ChildSession.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\PendingToken.xaml" />
		<Page Include="Views\WorkitemQueue.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\WorkItemQueuesView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\PackageManager.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\WorkflowInstances.xaml">
			<SubType>Designer</SubType>
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\InsertSelect.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\RecorderPlugins.xaml">
			<SubType>Designer</SubType>
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\RunPlugins.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\Snippets.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\SplashWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
	</ItemGroup>
	<ItemGroup>
		<Resource Include="Resources\Buttons\Button.png" />
		<Resource Include="Resources\Buttons\OpenEdge.png" />
		<Resource Include="Resources\Buttons\OpenFirefox.png" />
		<Resource Include="Resources\Buttons\Permissions.png" />
		<Resource Include="Resources\Buttons\picture_in_picture.png" />
		<Resource Include="Resources\Buttons\Reload.png" />
		<Resource Include="Resources\Buttons\Revert.png" />
		<Resource Include="Resources\Buttons\Signout.png" />
		<Resource Include="Resources\Buttons\swap.png" />
		<Resource Include="Resources\icons\close.png" />
		<Resource Include="Resources\designer\addworkitem.png" />
		<Resource Include="Resources\designer\break.png" />
		<Resource Include="Resources\designer\bulkaddworkitems.png" />
		<Resource Include="Resources\designer\continue.png" />
		<Resource Include="Resources\designer\deleteworkitem.png" />
		<Resource Include="Resources\designer\dowhile.png" />
		<Resource Include="Resources\designer\getworkflowinstance.png" />
		<Resource Include="Resources\designer\invokeremoterpaworkflow.png" />
		<Resource Include="Resources\designer\mouse.png" />
		<Resource Include="Resources\designer\moveelement.png" />
		<Resource Include="Resources\designer\popworkitem.png" />
		<Resource Include="Resources\designer\showballontip.png" />
		<Resource Include="Resources\designer\stopopnerpa.png" />
		<Resource Include="Resources\designer\throwbusinessruleexception.png" />
		<Resource Include="Resources\designer\updateworkitem.png" />
		<Resource Include="Resources\designer\while.png" />
		<Resource Include="Resources\loadingbackground.jpg" />
		<Resource Include="Resources\icons\activity.png" />
		<Resource Include="Resources\icons\flowchart.png" />
		<Resource Include="Resources\icons\openin.png" />
		<Resource Include="Resources\icons\property.png" />
		<Resource Include="Resources\icons\spinner2.png" />
		<Resource Include="Resources\icons\variable.png" />
		<Resource Include="Resources\icons\open.png" />
		<Resource Include="Resources\sxrpa.ico" />
		<EmbeddedResource Include="Resources\toolbox\addworkitem.png" />
		<EmbeddedResource Include="Resources\toolbox\bulkaddworkitems.png" />
		<EmbeddedResource Include="Resources\toolbox\commentout.png" />
		<EmbeddedResource Include="Resources\toolbox\copyclipboard.png" />
		<EmbeddedResource Include="Resources\toolbox\deleteworkitem.png" />
		<EmbeddedResource Include="Resources\toolbox\detector.png" />
		<EmbeddedResource Include="Resources\toolbox\dowhile.png" />
		<EmbeddedResource Include="Resources\toolbox\elementclick.png" />
		<EmbeddedResource Include="Resources\toolbox\focuselement.png" />
		<EmbeddedResource Include="Resources\toolbox\foreach.png" />
		<EmbeddedResource Include="Resources\toolbox\invokeremoterpaworkflow.png" />
		<EmbeddedResource Include="Resources\toolbox\openapplication.png" />
		<EmbeddedResource Include="Resources\toolbox\getworkflowinstance.png" />
		<EmbeddedResource Include="Resources\toolbox\highlightelement.png" />
		<EmbeddedResource Include="Resources\toolbox\insertclipboard.png" />
		<EmbeddedResource Include="Resources\toolbox\invokerpaworkflow.png" />
		<EmbeddedResource Include="Resources\toolbox\invokeopenflow.png" />
		<EmbeddedResource Include="Resources\toolbox\closeapplication.png" />
		<EmbeddedResource Include="Resources\toolbox\moveelement.png" />
		<EmbeddedResource Include="Resources\toolbox\popworkitem.png" />
		<EmbeddedResource Include="Resources\toolbox\showballontip.png" />
		<EmbeddedResource Include="Resources\toolbox\stopopnerpa.png" />
		<EmbeddedResource Include="Resources\toolbox\throwbusinessruleexception.png" />
		<EmbeddedResource Include="Resources\toolbox\typetext.png" />
		<EmbeddedResource Include="Resources\toolbox\updateworkitem.png" />
		<EmbeddedResource Include="Resources\toolbox\while.png" />
		<EmbeddedResource Include="Resources\snippet.png" />
	</ItemGroup>
	<ItemGroup>
		<Page Include="MainWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\CloseableHeader.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\DetectorsView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\EditXAML.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\InsertText.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\KeyboardSeqWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\OpenProject.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\PermissionsWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\selectUserWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\showVariables.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\WFDesigner.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\wfToolbox.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
		<Page Include="Views\ServerConfigWindow.xaml">
			<Generator>MSBuild:Compile</Generator>
		</Page>
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\OpenRPA.CodeEditor\OpenRPA.CodeEditor.csproj" />
		<ProjectReference Include="..\OpenRPA.Net\OpenRPA.Net.csproj">
			<IncludeAssets>all</IncludeAssets>
		</ProjectReference>
		<ProjectReference Include="..\OpenRPA.NM\OpenRPA.NM.csproj">
			<IncludeAssets>all</IncludeAssets>
		</ProjectReference>
		<ProjectReference Include="..\OpenRPA.Windows\OpenRPA.Windows.csproj">
			<IncludeAssets>all</IncludeAssets>
		</ProjectReference>
	</ItemGroup>
	<ItemGroup>
		<Reference Include="AxInterop.MSTSCLib">
			<HintPath>lib\AxInterop.MSTSCLib.dll</HintPath>
		</Reference>
		<Reference Include="Microsoft.CSharp" />
		<Reference Include="Microsoft.VisualBasic" />
		<Reference Include="MSTSCLib">
			<HintPath>lib\MSTSCLib.dll</HintPath>
		</Reference>
		<Reference Include="PresentationCore" />
		<Reference Include="PresentationFramework" />
		<Reference Include="System.Activities" />
		<Reference Include="System.Activities.Core.Presentation" />
		<Reference Include="System.Activities.DurableInstancing" />
		<Reference Include="System.Activities.Presentation" />
		<Reference Include="System.Data.DataSetExtensions" />
		<Reference Include="System.Runtime.DurableInstancing" />
		<Reference Include="System.Runtime.Remoting" />
		<Reference Include="System.Windows.Controls.Ribbon" />
		<Reference Include="System.Xaml" />
		<Reference Include="UIAutomationClient" />
		<Reference Include="WindowsBase" />
		<Reference Include="WindowsFormsIntegration" />
	</ItemGroup>
	<ItemGroup>
		<Resource Include="Images\PinDocMenu_Black.png" />
		<Resource Include="Resources\Buttons\Copy.png" />
		<Resource Include="Resources\Buttons\Delete.png" />
		<Resource Include="Resources\Buttons\Export.png" />
		<Resource Include="Resources\Buttons\Import.png" />
		<Resource Include="Resources\Buttons\New.png" />
		<Resource Include="Resources\Buttons\Open.png" />
		<Resource Include="Resources\Buttons\OpenChrome.png" />
		<Resource Include="Resources\Buttons\Play.png" />
		<Resource Include="Resources\Buttons\Record.png" />
		<Resource Include="Resources\Buttons\Save.png" />
		<Resource Include="Resources\Buttons\Exit.png" />
		<Resource Include="Resources\Buttons\Stop.png" />
		<Resource Include="Resources\designer\commentout.png" />
		<Resource Include="Resources\designer\copyclipboard.png" />
		<Resource Include="Resources\designer\detector.png" />
		<Resource Include="Resources\designer\elementclick.png" />
		<Resource Include="Resources\designer\focuselement.png" />
		<Resource Include="Resources\designer\foreach.png" />
		<Resource Include="Resources\designer\openapplication.png" />
		<Resource Include="Resources\designer\highlightelement.png" />
		<Resource Include="Resources\designer\insertclipboard.png" />
		<Resource Include="Resources\designer\invokerpaworkflow.png" />
		<Resource Include="Resources\designer\invokeopenflow.png" />
		<Resource Include="Resources\designer\closeapplication.png" />
		<Resource Include="Resources\designer\typetext.png" />
		<Resource Include="Resources\open_rpa_white.png" />
		<Resource Include="Resources\state\Abort.png" />
		<Resource Include="Resources\state\Completed.png" />
		<Resource Include="Resources\state\failed.png" />
		<Resource Include="Resources\state\Risk.png" />
		<Resource Include="Resources\state\Running.png" />
		<Resource Include="Resources\state\Running_green.png" />
		<Resource Include="Resources\state\sensor.png" />
		<Resource Include="Resources\state\unloaded.png" />
		<Resource Include="Resources\state\workitemqueue.png" />
		<EmbeddedResource Include="Resources\toolbox\continue.png" />
		<EmbeddedResource Include="Resources\toolbox\break.png" />
		<EmbeddedResource Include="Resources\toolbox\mouse.png" />
	</ItemGroup>
	<ItemGroup>
		<Compile Update="Activities\StopOpenRPADesigner.xaml.cs">
			<Generator></Generator>
		</Compile>
		<Compile Update="Resources\strings.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>strings.resx</DependentUpon>
		</Compile>
		<Compile Update="Views\RecorderPlugins.xaml.cs">
			<SubType>Code</SubType>
		</Compile>
		<Compile Update="Views\WorkflowInstances.xaml.cs">
			<SubType>Code</SubType>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Update="Resources\strings.ru.resx">
			<LastGenOutput>strings.Designer.cs</LastGenOutput>
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\strings.ro.resx">
			<LastGenOutput>strings.Designer.cs</LastGenOutput>
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\strings.es.resx">
			<SubType>Designer</SubType>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\strings.da.resx">
			<Generator></Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\strings.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>strings.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\strings.zh.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\strings.en.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<None Update="Microsoft.Expression.Interactions.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="System.Windows.Interactivity.dll">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
		<Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
	</Target>
	<!--<Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="ECHO ON&#xD;&#xA;nuget.exe push -Source &quot;nuget.org&quot; $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg" />
  </Target>-->
	<Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
		<Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
	</Target>
	<Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
		<Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
	</Target>
	<ProjectExtensions>
		<VisualStudio>
			<UserProperties BuildVersion_BuildVersioningStyle="None.None.Increment.None" BuildVersion_StartDate="2000/1/1" />
		</VisualStudio>
	</ProjectExtensions>
	<!-- 构建后事件：复制浏览器扩展包文件和Python嵌入式文件夹 -->
<Target Name="PostBuild" AfterTargets="PostBuildEvent">
	<!-- 确保在构建前先构建扩展包 -->
	<Exec Command="cd &quot;$(MSBuildProjectDirectory)\..\OpenRPA.NativeMessagingHost&quot; &amp;&amp; build_extensions.bat" ContinueOnError="true" />

	<!-- 复制浏览器扩展包文件到输出目录 -->
	<ItemGroup>
		<ExtensionFiles Include="$(MSBuildProjectDirectory)\obj\$(Configuration)\$(TargetFramework)\sxrpa-*.zip" />
		<ExtensionFiles Include="$(MSBuildProjectDirectory)\obj\$(Configuration)\$(TargetFramework)\sxrpa-*.xpi" />
	</ItemGroup>
	<Copy SourceFiles="@(ExtensionFiles)" DestinationFolder="$(OutputPath)" SkipUnchangedFiles="true" />

	<!-- 复制Python嵌入式文件夹到输出目录（用于开发环境） -->
	<PropertyGroup>
		<PythonEmbeddedSource>$(MSBuildProjectDirectory)\..\python_embedded</PythonEmbeddedSource>
		<PythonOutputDir>$(OutputPath)python</PythonOutputDir>
	</PropertyGroup>

	<!-- 检查并复制Python 3.7 -->
	<ItemGroup Condition="Exists('$(PythonEmbeddedSource)\python-3.7.9-embed-amd64')">
		<Python37Files Include="$(PythonEmbeddedSource)\python-3.7.9-embed-amd64\**\*" />
	</ItemGroup>
	<MakeDir Directories="$(PythonOutputDir)\python-3.7.9-embed-amd64" Condition="'@(Python37Files)' != ''" />
	<Copy SourceFiles="@(Python37Files)" DestinationFiles="@(Python37Files->'$(PythonOutputDir)\python-3.7.9-embed-amd64\%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(Python37Files)' != ''" />

	<!-- 检查并复制Python 3.10 -->
	<ItemGroup Condition="Exists('$(PythonEmbeddedSource)\python-3.10.11-embed-amd64')">
		<Python310Files Include="$(PythonEmbeddedSource)\python-3.10.11-embed-amd64\**\*" />
	</ItemGroup>
	<MakeDir Directories="$(PythonOutputDir)\python-3.10.11-embed-amd64" Condition="'@(Python310Files)' != ''" />
	<Copy SourceFiles="@(Python310Files)" DestinationFiles="@(Python310Files->'$(PythonOutputDir)\python-3.10.11-embed-amd64\%(RecursiveDir)%(Filename)%(Extension)')" SkipUnchangedFiles="true" Condition="'@(Python310Files)' != ''" />

	<!-- Python.NET兼容性：为Python 3.10创建python37.dll（复制python310.dll为python37.dll） -->
	<PropertyGroup>
		<Python310DllSource>$(PythonOutputDir)\python-3.10.11-embed-amd64\python310.dll</Python310DllSource>
		<Python37DllTarget>$(PythonOutputDir)\python-3.10.11-embed-amd64\python37.dll</Python37DllTarget>
	</PropertyGroup>
	<Copy SourceFiles="$(Python310DllSource)" DestinationFiles="$(Python37DllTarget)" SkipUnchangedFiles="true" Condition="Exists('$(Python310DllSource)') AND !Exists('$(Python37DllTarget)')" />
	<Message Text="Created python37.dll compatibility file for Python.NET" Importance="high" Condition="Exists('$(Python310DllSource)') AND !Exists('$(Python37DllTarget)')" />

	<!-- 输出复制状态信息 -->
	<Message Text="Python embedded folders copied to: $(PythonOutputDir)" Importance="high" Condition="'@(Python37Files)' != '' OR '@(Python310Files)' != ''" />
	<Warning Text="Python embedded folders not found in: $(PythonEmbeddedSource). Run download_python.ps1 to download them." Condition="'@(Python37Files)' == '' AND '@(Python310Files)' == ''" />
</Target>
</Project>
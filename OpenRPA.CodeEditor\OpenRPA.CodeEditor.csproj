﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <EnableDefaultPageItems>false</EnableDefaultPageItems>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <PackageIcon>codeeditor.png</PackageIcon>
    <PackageIconUrl />
    <Authors><PERSON></Authors>
    <Product>OpenRPA</Product>
    <Description>Code Edtior with syntax Coloring and intellisense for Microsoft Workflow Foundation, specifically tested and created for OpenRPA robot</Description>
    <Version>1.0.3</Version>
    <PackageReleaseNotes>1.0.3 Ignore unknown tags
1.0.2 Change target framework to 4.7.2
1.0.1 Downgrade roslyn compiler to avoid https://github.com/dotnet/roslyn/issues/38779</PackageReleaseNotes>
    <Configurations>Debug;Release;PrepInstaller;ReleaseNuget</Configurations>
    <PackageTags></PackageTags>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <Platforms>AnyCPU</Platforms>
    <Company>OpenIAP</Company>
    <!-- 限制资源文件只生成简体中文和英文 -->
    <SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseNuget|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Resources\CodeEditorResources.xaml" />
    <None Remove="Resources\ImageResources.xaml" />
    <None Remove="Resources\Images\Class16.png" />
    <None Remove="Resources\Images\Constant16.png" />
    <None Remove="Resources\Images\Copy16.png" />
    <None Remove="Resources\Images\Cut16.png" />
    <None Remove="Resources\Images\Delegate16.png" />
    <None Remove="Resources\Images\Enum16.png" />
    <None Remove="Resources\Images\EnumItem16.png" />
    <None Remove="Resources\Images\Error16.png" />
    <None Remove="Resources\Images\Event16.png" />
    <None Remove="Resources\Images\ExtensionMethod16.png" />
    <None Remove="Resources\Images\Field16.png" />
    <None Remove="Resources\Images\Info16.png" />
    <None Remove="Resources\Images\Interface16.png" />
    <None Remove="Resources\Images\Keyword16.png" />
    <None Remove="Resources\Images\Method16.png" />
    <None Remove="Resources\Images\Module16.png" />
    <None Remove="Resources\Images\Namespace16.png" />
    <None Remove="Resources\Images\Paste16.png" />
    <None Remove="Resources\Images\Property16.png" />
    <None Remove="Resources\Images\Start16.png" />
    <None Remove="Resources\Images\Stop16.png" />
    <None Remove="Resources\Images\Structure16.png" />
    <None Remove="Resources\Images\Warning16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\codeeditor.png" Pack="true" PackagePath="\" />
    <PackageReference Include="AvalonEdit" Version="6.1.3.50" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Features" Version="4.0.1" />
    <PackageReference Include="Microsoft.CodeAnalysis.VisualBasic.Features" Version="4.0.1" />
    <PackageReference Include="Microsoft.VisualStudio.Debugger.Contracts" Version="17.2.0" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Activities" />
    <Reference Include="System.Activities.Presentation" />
    <Reference Include="System.Xaml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Resources\CodeEditorResources.xaml" />
    <Page Include="Resources\ImageResources.xaml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.Interfaces\OpenRPA.Interfaces.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\Images\Class16.png" />
    <Resource Include="Resources\Images\Constant16.png" />
    <Resource Include="Resources\Images\Copy16.png" />
    <Resource Include="Resources\Images\Cut16.png" />
    <Resource Include="Resources\Images\Delegate16.png" />
    <Resource Include="Resources\Images\Enum16.png" />
    <Resource Include="Resources\Images\EnumItem16.png" />
    <Resource Include="Resources\Images\Error16.png" />
    <Resource Include="Resources\Images\Event16.png" />
    <Resource Include="Resources\Images\ExtensionMethod16.png" />
    <Resource Include="Resources\Images\Field16.png" />
    <Resource Include="Resources\Images\Info16.png" />
    <Resource Include="Resources\Images\Interface16.png" />
    <Resource Include="Resources\Images\Keyword16.png" />
    <Resource Include="Resources\Images\Method16.png" />
    <Resource Include="Resources\Images\Module16.png" />
    <Resource Include="Resources\Images\Namespace16.png" />
    <Resource Include="Resources\Images\Paste16.png" />
    <Resource Include="Resources\Images\Property16.png" />
    <Resource Include="Resources\Images\Start16.png" />
    <Resource Include="Resources\Images\Stop16.png" />
    <Resource Include="Resources\Images\Structure16.png" />
    <Resource Include="Resources\Images\Warning16.png" />
  </ItemGroup>
  <Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
</Project>
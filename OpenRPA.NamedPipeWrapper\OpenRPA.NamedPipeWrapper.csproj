<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Authors><PERSON></Authors>
    <PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <PackageId>OpenRPA.NamedPipeWrapper</PackageId>
    <Version>1.0.17</Version>
    <PackageReleaseNotes>1.0.18 Add thread names to improve debugging
1.0.17 bump version
1.0.15 update interfaces reference
1.0.12 update reference
1.0.11 update interfaces reference</PackageReleaseNotes>
    <Description>Helper classes that makes named pipe communication easier, created for OpenRPA robot</Description>
    <PackageIcon>open_rpa128.png</PackageIcon>
    <Configurations>Debug;Release;ReleaseNuget;PrepInstaller</Configurations>
    <PackageTags></PackageTags>
    <Platforms>AnyCPU</Platforms>
    <Company>OpenIAP</Company>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath></OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath />
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <None Include="Resources\open_rpa128.png" Pack="true" PackagePath="\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
  </ItemGroup>
  <Target Name="SetNuspecProperties" BeforeTargets="GenerateNuspec">
    <PropertyGroup>
      <NuspecProperties>$(NuspecProperties);id=$(AssemblyName)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);config=$(Configuration)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);version=$(PackageVersion)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);description=$(Description)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);author=$(Authors)</NuspecProperties>
    </PropertyGroup>
  </Target>
  <Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <ProjectExtensions><VisualStudio><UserProperties BuildVersion_StartDate="2000/1/1" /></VisualStudio></ProjectExtensions>
</Project>